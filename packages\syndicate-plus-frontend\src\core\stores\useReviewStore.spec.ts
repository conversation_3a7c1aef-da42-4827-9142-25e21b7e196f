import { describe, it, expect, beforeEach } from 'vitest';
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia';
import { useReviewStore } from './useReviewStore';
import { ReviewBatchData, ValidationRow } from '@core/interfaces';
import { ErrorType } from '@core/enums';

describe('useReviewStore - calculateStatistics', () => {
  let store: ReturnType<typeof useReviewStore>;

  beforeEach(() => {
    setActivePinia(createPinia());
    store = useReviewStore();
  });

  describe('calculateStatistics', () => {
    it('should handle undefined reviewData and set total to 0', () => {
      // Arrange
      store.reviewData = [];
      store.reviewValidation = [];

      // Act
      store.calculateStatistics();

      // Assert
      expect(store.total).toBe(0);
      expect(store.failed).toBe(0);
      expect(store.passed).toBe(0);
    });

    it('should handle empty reviewData and set total to 0', () => {
      // Arrange
      store.reviewData = [];
      store.reviewValidation = [];

      // Act
      store.calculateStatistics();

      // Assert
      expect(store.total).toBe(0);
      expect(store.failed).toBe(0);
      expect(store.passed).toBe(0);
    });

    it('should correctly calculate statistics with no validation errors', () => {
      // Arrange
      const mockReviewData: ReviewBatchData[] = [
        { Id: 1, Fields: [] },
        { Id: 2, Fields: [] },
        { Id: 3, Fields: [] },
        { Id: 4, Fields: [] },
        { Id: 5, Fields: [] },
        { Id: 6, Fields: [] },
        { Id: 7, Fields: [] },
        { Id: 8, Fields: [] },
        { Id: 9, Fields: [] },
        { Id: 10, Fields: [] },
      ];

      const mockValidation: ValidationRow[] = [
        {
          EntityId: 1,
          Warnings: [],
          Errors: [],
        },
        {
          EntityId: 2,
          Warnings: [],
          Errors: [],
        },
        {
          EntityId: 3,
          Warnings: [],
          Errors: [],
        },
      ];

      store.reviewData = mockReviewData;
      store.reviewValidation = mockValidation;

      // Act
      store.calculateStatistics();

      // Assert
      expect(store.total).toBe(10); // Based on reviewData.value.length
      expect(store.failed).toBe(0);
      expect(store.passed).toBe(10);
    });

    it('should correctly calculate statistics with some validation errors', () => {
      // Arrange
      const mockReviewData: ReviewBatchData[] = [
        { Id: 1, Fields: [] },
        { Id: 2, Fields: [] },
        { Id: 3, Fields: [] },
        { Id: 4, Fields: [] },
        { Id: 5, Fields: [] },
      ];

      const mockValidation: ValidationRow[] = [
        {
          EntityId: 1,
          Warnings: [],
          Errors: [
            {
              FieldId: 'field1',
              FormatFieldId: 1,
              ErrorType: ErrorType.MANDATORY,
            },
          ],
        },
        {
          EntityId: 2,
          Warnings: [],
          Errors: [],
        },
        {
          EntityId: 3,
          Warnings: [],
          Errors: [
            {
              FieldId: 'field2',
              FormatFieldId: 2,
              ErrorType: ErrorType.RECOMMENDED,
            },
            {
              FieldId: 'field3',
              FormatFieldId: 3,
              ErrorType: ErrorType.MANDATORY,
            },
          ],
        },
      ];

      store.reviewData = mockReviewData;
      store.reviewValidation = mockValidation;

      // Act
      store.calculateStatistics();

      // Assert
      expect(store.total).toBe(5); // Based on reviewData.value.length
      expect(store.failed).toBe(2); // Two rows have errors
      expect(store.passed).toBe(3); // 5 - 2 = 3
    });

    it('should correctly calculate statistics when all validations have errors', () => {
      // Arrange
      const mockReviewData: ReviewBatchData[] = [
        { Id: 1, Fields: [] },
        { Id: 2, Fields: [] },
        { Id: 3, Fields: [] },
      ];

      const mockValidation: ValidationRow[] = [
        {
          EntityId: 1,
          Warnings: [],
          Errors: [
            {
              FieldId: 'field1',
              FormatFieldId: 1,
              ErrorType: ErrorType.MANDATORY,
            },
          ],
        },
        {
          EntityId: 2,
          Warnings: [],
          Errors: [
            {
              FieldId: 'field2',
              FormatFieldId: 2,
              ErrorType: ErrorType.RECOMMENDED,
            },
          ],
        },
        {
          EntityId: 3,
          Warnings: [],
          Errors: [
            {
              FieldId: 'field3',
              FormatFieldId: 3,
              ErrorType: ErrorType.MANDATORY,
            },
          ],
        },
      ];

      store.reviewData = mockReviewData;
      store.reviewValidation = mockValidation;

      // Act
      store.calculateStatistics();

      // Assert
      expect(store.total).toBe(3); // Based on reviewData.value.length
      expect(store.failed).toBe(3);
      expect(store.passed).toBe(0);
    });

    it('should handle empty validation array', () => {
      // Arrange
      store.reviewData = [];
      store.reviewValidation = [];

      // Act
      store.calculateStatistics();

      // Assert
      expect(store.total).toBe(0); // Based on reviewData.value.length
      expect(store.failed).toBe(0);
      expect(store.passed).toBe(0);
    });

    it('should handle mismatch between review data and validation rows', () => {
      // Arrange - 10 review data items but only 2 validation rows exist
      const mockReviewData: ReviewBatchData[] = Array.from(
        { length: 10 },
        (_, i) =>
          ({
            Id: i + 1,
            Fields: [],
          } as ReviewBatchData)
      );

      const mockValidation: ValidationRow[] = [
        {
          EntityId: 1,
          Warnings: [],
          Errors: [
            {
              FieldId: 'field1',
              FormatFieldId: 1,
              ErrorType: ErrorType.MANDATORY,
            },
          ],
        },
        {
          EntityId: 2,
          Warnings: [],
          Errors: [],
        },
      ];

      store.reviewData = mockReviewData;
      store.reviewValidation = mockValidation;

      // Act
      store.calculateStatistics();

      // Assert
      expect(store.total).toBe(10); // Based on reviewData.value.length
      expect(store.failed).toBe(1); // Based on actual validation rows with errors
      expect(store.passed).toBe(9); // 10 - 1 = 9
    });

    it('should ensure passed count does not become negative when failed exceeds total', () => {
      // Arrange - 2 review data items but 3 validation rows have errors
      const mockReviewData: ReviewBatchData[] = [
        { Id: 1, Fields: [] },
        { Id: 2, Fields: [] },
      ];

      const mockValidation: ValidationRow[] = [
        {
          EntityId: 1,
          Warnings: [],
          Errors: [
            {
              FieldId: 'field1',
              FormatFieldId: 1,
              ErrorType: ErrorType.MANDATORY,
            },
          ],
        },
        {
          EntityId: 2,
          Warnings: [],
          Errors: [
            {
              FieldId: 'field2',
              FormatFieldId: 2,
              ErrorType: ErrorType.MANDATORY,
            },
          ],
        },
        {
          EntityId: 3,
          Warnings: [],
          Errors: [
            {
              FieldId: 'field3',
              FormatFieldId: 3,
              ErrorType: ErrorType.MANDATORY,
            },
          ],
        },
      ];

      store.reviewData = mockReviewData;
      store.reviewValidation = mockValidation;

      // Act
      store.calculateStatistics();

      // Assert
      expect(store.total).toBe(2); // Based on reviewData.value.length
      expect(store.failed).toBe(3); // Based on actual validation rows with errors
      expect(store.passed).toBe(0); // 2 - 3 = -1, but we ensure it doesn't become negative
    });
  });
});
