import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AmazonCallbackStatus, AmazonCallbackUrlParams } from '@enums/Amazon';
import { parseAmazonAuthStatusUrlSearchParam, parseReturnFromAmazonUrlSearchParam } from './amazonUrlSearchParamUtils';

// Mock window.location
const mockLocation = {
  search: '',
};

Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

describe('Amazon URL Search Param Utils', () => {
  beforeEach(() => {
    // Reset location search before each test
    mockLocation.search = '';
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('parseAmazonAuthStatusUrlSearchParam', () => {
    it('when status parameter is "success", should return AmazonCallbackStatus.SUCCESS', () => {
      // Arrange
      mockLocation.search = `?${AmazonCallbackUrlParams.STATUS}=${AmazonCallbackStatus.SUCCESS}`;

      // Act
      const result = parseAmazonAuthStatusUrlSearchParam();

      // Assert
      expect(result).toBe(AmazonCallbackStatus.SUCCESS);
    });

    it('when status parameter is "error", should return AmazonCallbackStatus.ERROR', () => {
      // Arrange
      mockLocation.search = `?${AmazonCallbackUrlParams.STATUS}=${AmazonCallbackStatus.ERROR}`;

      // Act
      const result = parseAmazonAuthStatusUrlSearchParam();

      // Assert
      expect(result).toBe(AmazonCallbackStatus.ERROR);
    });

    it('when status parameter is not provided, should return null', () => {
      // Arrange
      mockLocation.search = '?some-other-param=value';

      // Act
      const result = parseAmazonAuthStatusUrlSearchParam();

      // Assert
      expect(result).toBeNull();
    });

    it('when status parameter is empty, should return null', () => {
      // Arrange
      mockLocation.search = `?${AmazonCallbackUrlParams.STATUS}=`;

      // Act
      const result = parseAmazonAuthStatusUrlSearchParam();

      // Assert
      expect(result).toBeNull();
    });

    it('when status parameter has invalid value, should return null', () => {
      // Arrange
      mockLocation.search = `?${AmazonCallbackUrlParams.STATUS}=invalid-status`;

      // Act
      const result = parseAmazonAuthStatusUrlSearchParam();

      // Assert
      expect(result).toBeNull();
    });

    it('when URL has no search parameters, should return null', () => {
      // Arrange
      mockLocation.search = '';

      // Act
      const result = parseAmazonAuthStatusUrlSearchParam();

      // Assert
      expect(result).toBeNull();
    });

    it('when multiple URL parameters are present, should work correctly', () => {
      // Arrange
      mockLocation.search = `?other-param=value&${AmazonCallbackUrlParams.STATUS}=success&another-param=test`;

      // Act
      const result = parseAmazonAuthStatusUrlSearchParam();

      // Assert
      expect(result).toBe(AmazonCallbackStatus.SUCCESS);
    });
  });

  describe('parseReturnFromAmazonUrlSearchParam', () => {
    it('when return-from-amazon parameter is "true", should return true', () => {
      // Arrange
      mockLocation.search = `?${AmazonCallbackUrlParams.RETURN_FROM_AMAZON}=true`;

      // Act
      const result = parseReturnFromAmazonUrlSearchParam();

      // Assert
      expect(result).toBe(true);
    });

    it('when return-from-amazon parameter is "1", should return false', () => {
      // Arrange
      mockLocation.search = `?${AmazonCallbackUrlParams.RETURN_FROM_AMAZON}=1`;

      // Act
      const result = parseReturnFromAmazonUrlSearchParam();

      // Assert
      expect(result).toBe(false);
    });

    it('when return-from-amazon parameter is any other truthy string, should return false', () => {
      // Arrange
      mockLocation.search = `?${AmazonCallbackUrlParams.RETURN_FROM_AMAZON}=yes`;

      // Act
      const result = parseReturnFromAmazonUrlSearchParam();

      // Assert
      expect(result).toBe(false);
    });

    it('when return-from-amazon parameter is "false", should return false', () => {
      // Arrange
      mockLocation.search = `?${AmazonCallbackUrlParams.RETURN_FROM_AMAZON}=false`;

      // Act
      const result = parseReturnFromAmazonUrlSearchParam();

      // Assert
      expect(result).toBe(false);
    });

    it('when return-from-amazon parameter is "TRUE" (case sensitive), should return true', () => {
      // Arrange
      mockLocation.search = `?${AmazonCallbackUrlParams.RETURN_FROM_AMAZON}=TRUE`;

      // Act
      const result = parseReturnFromAmazonUrlSearchParam();

      // Assert
      expect(result).toBe(true);
    });

    it('when return-from-amazon parameter is empty, should return false', () => {
      // Arrange
      mockLocation.search = `?${AmazonCallbackUrlParams.RETURN_FROM_AMAZON}=`;

      // Act
      const result = parseReturnFromAmazonUrlSearchParam();

      // Assert
      expect(result).toBe(false);
    });

    it('when return-from-amazon parameter is not provided, should return false', () => {
      // Arrange
      mockLocation.search = '?some-other-param=value';

      // Act
      const result = parseReturnFromAmazonUrlSearchParam();

      // Assert
      expect(result).toBe(false);
    });

    it('when URL has no search parameters, should return false', () => {
      // Arrange
      mockLocation.search = '';

      // Act
      const result = parseReturnFromAmazonUrlSearchParam();

      // Assert
      expect(result).toBe(false);
    });

    it('when multiple URL parameters are present, should work correctly', () => {
      // Arrange
      mockLocation.search = `?other-param=value&${AmazonCallbackUrlParams.RETURN_FROM_AMAZON}=true&another-param=test`;

      // Act
      const result = parseReturnFromAmazonUrlSearchParam();

      // Assert
      expect(result).toBe(true);
    });
  });

  describe('Integration tests', () => {
    it('when both parameters are present in URL, should handle correctly', () => {
      // Arrange
      mockLocation.search = `?${AmazonCallbackUrlParams.STATUS}=success&${AmazonCallbackUrlParams.RETURN_FROM_AMAZON}=true`;

      // Act
      const statusResult = parseAmazonAuthStatusUrlSearchParam();
      const returnResult = parseReturnFromAmazonUrlSearchParam();

      // Assert
      expect(statusResult).toBe(AmazonCallbackStatus.SUCCESS);
      expect(returnResult).toBe(true);
    });

    it('when Amazon callback URL has error status, should handle correctly', () => {
      // Arrange
      mockLocation.search = `?${AmazonCallbackUrlParams.STATUS}=error&${AmazonCallbackUrlParams.RETURN_FROM_AMAZON}=true`;

      // Act
      const statusResult = parseAmazonAuthStatusUrlSearchParam();
      const returnResult = parseReturnFromAmazonUrlSearchParam();

      // Assert
      expect(statusResult).toBe(AmazonCallbackStatus.ERROR);
      expect(returnResult).toBe(true);
    });
  });
});
