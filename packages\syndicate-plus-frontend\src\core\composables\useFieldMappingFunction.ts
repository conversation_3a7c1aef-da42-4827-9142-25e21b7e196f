import { FunctionModel } from '@core/interfaces';
import { computed, ref } from 'vue';

export default function useFieldMappingFunction() {
  // Refs
  const functionName = ref('');

  // Computed
  const hasFunction = computed(() => !!functionName.value);

  // Functions
  const parseFunctionName = (converterArgs: string | undefined): void => {
    let name = '';
    if (converterArgs) {
      const regex = /"name"\s*:\s*"([^"]+)"/;
      const match = converterArgs.match(regex);

      name = match ? match[1] : '';
    }

    functionName.value = name;
  };

  const matchFunctionName = (id: number, functions: FunctionModel[]): void => {
    const functionModel = functions.find((x) => x.Id === id);
    functionName.value = functionModel?.Name ?? '';
  };

  return {
    functionName,
    hasFunction,
    parseFunctionName,
    matchFunctionName,
  };
}
