import { ref } from 'vue';
import BannerOptions from './bannerOptions';

// Banner management state
const bannerState = ref({
  success: { show: false, message: '', showClose: false },
  info: { show: false, message: '', showClose: false },
  warning: { show: false, message: '', showClose: false },
  error: { show: false, message: '', showClose: false },
});

// Banner functions
const banner = {
  success: (message = '', options: BannerOptions = {}) => {
    const { timeout = 0, showClose = false } = options;
    bannerState.value.success = { show: true, message, showClose };
    if (timeout > 0) {
      setTimeout(() => {
        bannerState.value.success.show = false;
      }, timeout);
    }
  },

  info: (message = '', options: BannerOptions = {}) => {
    const { timeout = 0, showClose = false } = options;
    bannerState.value.info = { show: true, message, showClose };
    if (timeout > 0) {
      setTimeout(() => {
        bannerState.value.info.show = false;
      }, timeout);
    }
  },

  warning: (message = '', options: BannerOptions = {}) => {
    const { timeout = 0, showClose = false } = options;
    bannerState.value.warning = { show: true, message, showClose };
    if (timeout > 0) {
      setTimeout(() => {
        bannerState.value.warning.show = false;
      }, timeout);
    }
  },

  error: (message = '', options: BannerOptions = {}) => {
    const { timeout = 0, showClose = false } = options;
    bannerState.value.error = { show: true, message, showClose };
    if (timeout > 0) {
      setTimeout(() => {
        bannerState.value.error.show = false;
      }, timeout);
    }
  },

  hide: (bannerType: 'success' | 'info' | 'warning' | 'error') => {
    bannerState.value[bannerType].show = false;
  },

  hideAll: () => {
    Object.keys(bannerState.value).forEach((bannerType) => {
      bannerState.value[bannerType].show = false;
    });
  },
};

export { bannerState, banner };
