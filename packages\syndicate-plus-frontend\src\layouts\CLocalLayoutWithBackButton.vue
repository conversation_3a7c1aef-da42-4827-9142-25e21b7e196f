<template>
  <c-page-blank class="border-t">
    <c-page-flex-col>
      <div v-if="!hideTitle" class="flex">
        <c-page-back-button />
        <slot name="header">
          <c-page-title :title="title" />
        </slot>
      </div>

      <c-page-outer-wrapper>
        <c-page-left-sidebar :style="{ width: leftSidebarWidth + 'px' }">
          <slot name="left-sidebar" />
        </c-page-left-sidebar>

        <c-page-flex-col>
          <c-page-auto-breadcrumbs v-if="showBreadcrumbs" />

          <c-page-main-content>
            <slot />
          </c-page-main-content>
        </c-page-flex-col>
      </c-page-outer-wrapper>
    </c-page-flex-col>

    <c-page-right-sidebar :style="{ width: rightSidebarWidth + 'px' }">
      <slot name="right-sidebar" />
    </c-page-right-sidebar>
  </c-page-blank>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: undefined,
  },
  showBreadcrumbs: <PERSON><PERSON><PERSON>,
  hideTitle: <PERSON><PERSON><PERSON>,
  leftSidebarWidth: {
    type: [String, Number],
    default: '60',
  },
  rightSidebarWidth: {
    type: [String, Number],
    default: '60',
  },
});
</script>
