import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { CBannerNotifications, banner } from './index';
import { nextTick } from 'vue';

describe('CBannerNotifications', () => {
  let wrapper: any;

  beforeEach(() => {
    // Arrange (fresh mount & cleared state)
    banner.hideAll();
    wrapper = mount(CBannerNotifications, {
      global: {
        // Light stubs for Quasar components (avoid full plugin setup)
        stubs: {
          QBanner: {
            name: 'QBanner',
            template:
              '<div class="q-banner" v-bind="$attrs"><slot name="avatar" /><slot /><slot name="action" /></div>',
          },
          QIcon: {
            name: 'QIcon',
            props: ['name'],
            template: '<i :data-name="name"><slot /></i>',
          },
          QBtn: {
            name: 'QBtn',
            props: ['icon'],
            emits: ['click'],
            template: '<button type="button" :data-icon="icon" @click="$emit(\'click\')"><slot /></button>',
          },
        },
      },
    });
  });

  afterEach(() => {
    wrapper?.unmount();
    banner.hideAll();
    vi.clearAllTimers();
  });

  describe('Banner Visibility', () => {
    it('when no banners are active, should not render banner container', async () => {
      // Arrange
      banner.hideAll();

      // Act
      await nextTick();

      // Assert
      expect(wrapper.vm.showBanners).toBe(false);
      expect(wrapper.find('[data-testid="banner-container"]').exists()).toBe(false);
    });

    it.each<['success' | 'info' | 'warning' | 'error', string, string]>([
      ['success', 'Operation completed successfully', 'mdi-check-circle-outline'],
      ['info', 'Information message', 'mdi-information-outline'],
      ['warning', 'Warning message', 'mdi-alert-outline'],
      ['error', 'Error occurred', 'mdi-alert-circle-outline'],
    ])('when %s banner is shown, should render banner with correct styling', async (type, message, icon) => {
      // Arrange
      banner[type](message);

      // Act
      await nextTick();
      await nextTick();

      // Assert
      const bannerElement = wrapper.find(`[data-testid="${type}-banner"]`);
      expect(bannerElement.exists()).toBe(true);
      expect(bannerElement.text()).toContain(message);
      expect(wrapper.find(`[data-name="${icon}"]`).exists()).toBe(true);
    });

    it('when banner state toggles, should update showBanners computed correctly', async () => {
      // Arrange
      expect(wrapper.vm.showBanners).toBe(false);

      // Act
      banner.success('Temporary');
      await nextTick();
      const afterShow = wrapper.vm.showBanners;
      banner.hide('success');
      await nextTick();

      // Assert
      expect(afterShow).toBe(true);
      expect(wrapper.vm.showBanners).toBe(false);
    });
  });

  describe('Close Button Functionality', () => {
    it('when banner has showClose enabled, should render close button', async () => {
      // Arrange
      const message = 'Closable banner';

      // Act
      banner.success(message, { showClose: true });
      await nextTick();

      // Assert
      const closeButton = wrapper.find('[data-testid="success-banner-close"]');
      expect(closeButton.exists()).toBe(true);
    });

    it('when banner has showClose disabled, should not render close button', async () => {
      // Arrange
      const message = 'Non-closable banner';

      // Act
      banner.success(message, { showClose: false });
      await nextTick();

      // Assert
      const closeButton = wrapper.find('[data-testid="success-banner-close"]');
      expect(closeButton.exists()).toBe(false);
    });

    it('when close button is clicked, should hide the banner', async () => {
      // Arrange
      const message = 'Closable banner';
      banner.success(message, { showClose: true });
      await nextTick();

      // Act
      const closeButton = wrapper.find('[data-testid="success-banner-close"]');
      await closeButton.trigger('click');
      await nextTick();

      // Assert
      expect(wrapper.find('[data-testid="success-banner"]').exists()).toBe(false);
      expect(wrapper.find('[data-testid="banner-container"]').exists()).toBe(false);
    });
  });

  describe('Banner State Integration', () => {
    it('when banner is hidden via banner.hide(), should remove banner from display', async () => {
      // Arrange
      banner.info('Test info message');
      await nextTick();
      // should be shown at this moment
      expect(wrapper.find('[data-testid="info-banner"]').exists()).toBe(true);

      // Act
      banner.hide('info');
      await nextTick();

      // Assert
      expect(wrapper.find('[data-testid="info-banner"]').exists()).toBe(false);
      expect(wrapper.find('[data-testid="banner-container"]').exists()).toBe(false);
    });

    it('when banner.hideAll() is called, should hide all banners', async () => {
      // Arrange
      banner.success('Success message');
      banner.error('Error message');
      await nextTick();
      expect(wrapper.find('[data-testid="error-banner"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="success-banner"]').exists()).toBe(true);

      // Act
      banner.hideAll();
      await nextTick();

      // Assert
      expect(wrapper.find('[data-testid="success-banner"]').exists()).toBe(false);
      expect(wrapper.find('[data-testid="error-banner"]').exists()).toBe(false);
      expect(wrapper.find('[data-testid="banner-container"]').exists()).toBe(false);
    });
  });
});
