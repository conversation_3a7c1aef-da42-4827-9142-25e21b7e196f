import { TradingPartnerAuthorizationResponse } from '@core/interfaces/TradingPartnerAuthorization';
import { outputAdapterClient } from '@core/Utils';
import { getEnvironmentGlobalId } from '@helpers/EnvironmentHelper';

export const startAmazonAuthFlow = async (tradingPartnerId: string, redirectUrl: string): Promise<void> => {
  const environmentGid = getEnvironmentGlobalId();
  const prefix = import.meta.env.DEV ? '/proxy' : '';
  const url =
    prefix +
    `/outputadapter/proxy/api/environments/${environmentGid}/trading-partners/${tradingPartnerId}/authorizationflow/start?redirectUrl=${encodeURIComponent(
      redirectUrl
    )}`;
  //await outputAdapterClient.get(url, 'Error starting Amazon authorization flow')
  window.location.href = url;
};

export const checkTradingPartnerAuthorization = async (tradingPartnerId: string): Promise<boolean> => {
  const environmentGid = getEnvironmentGlobalId();
  const url = `/api/environments/${environmentGid}/trading-partners/${tradingPartnerId}/authorizationflow/status`;
  const response = await outputAdapterClient.get<TradingPartnerAuthorizationResponse>(
    url,
    'Error checking trading partner authorization status'
  );
  return response.data?.IsAuthorized ?? false;
};
