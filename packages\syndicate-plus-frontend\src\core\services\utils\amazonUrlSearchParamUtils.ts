import { AmazonCallbackStatus, AmazonCallbackUrlParams } from '@enums/Amazon';
import { getEnumKeyByValue } from '@helpers/EnumHelper';

/**
 * Parses the Amazon authentication status from URL search parameters
 * @returns AmazonCallbackStatus enum value or null if not found
 */
export const parseAmazonAuthStatusUrlSearchParam = (): AmazonCallbackStatus | null => {
  const urlParams = new URLSearchParams(window.location.search);
  const statusString = urlParams.get(AmazonCallbackUrlParams.STATUS);
  if (!statusString) {
    return null;
  }

  const key = getEnumKeyByValue(AmazonCallbackStatus, statusString);
  return key ? AmazonCallbackStatus[key] : null;
};

/**
 * Parses the Amazon return flag from URL search parameters
 * @returns boolean indicating if the user returned from Amazon authorization
 */
export const parseReturnFromAmazonUrlSearchParam = (): boolean => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(AmazonCallbackUrlParams.RETURN_FROM_AMAZON)?.toLowerCase() === 'true';
};
