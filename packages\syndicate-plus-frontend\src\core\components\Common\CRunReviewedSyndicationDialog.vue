<template>
  <c-dialog
    v-model="showDialog"
    class="c-dialog"
    :title="$t('core.trading_partners.syndicate')"
    :confirm-button-text="$t('syndicate_plus.common.save')"
    :is-loading="isLoading"
    :disable-confirm="confirmIsDisabled"
    @confirm="confirm"
    @cancel="cancel"
  >
    <div class="w-1/2">
      <c-select
        v-model="selectedOutput"
        :options="outputs"
        :label="$t('core.trading_partners.collections.select_output')"
        :placeholder="$t('core.trading_partners.collections.select_output')"
        :option-label="getOptionLabel"
        option-value="ExtensionId"
        hide-bottom-space
        clearable
      />
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { onBeforeMount, watchEffect, ref, computed } from 'vue';
import { useDialog, notify } from '@inriver/inri';
import { useRunSyndicationDialog } from '@core/composables';
import { useI18n } from 'vue-i18n';
import { useCoreTradingPartnersStore, useCurrentCoreTradingPartnerStore } from '@core/stores';
import { storeToRefs } from 'pinia';
import { useRouter } from '@composables/useRouter';
import { DynamicMappingResponse, Mapping, Output, ChannelLinkResponse } from '@core/interfaces';
import { Collection, Workarea } from '@core/interfaces/Workarea';
import { CollectionTypes, MappingTypes, ResponseStatus } from '@core/enums';
import { fetchDynamicAssignedOutputs } from '@core/services/Outputs/assignedDynamicOutputsApi';
import { CoreOutputLinkResponse, DynamicOutputLinkResponse } from '@core/interfaces/Outputs';
import { fetchCoreAssignedOutputs } from '@core/services/Outputs';

const props = defineProps({
  mappingId: {
    type: Number,
    required: true,
  },
  isDynamic: {
    type: Boolean,
    default: false,
  },
  selectedEntityIds: {
    type: Array<number>,
    required: true,
    default: () => [],
  },
  collectionName: {
    type: String,
    required: true,
  },
  collectionType: {
    type: String,
    default: 'entities',
  },
  workareaId: {
    type: String,
    default: '',
  },
  channelId: {
    type: Number,
    default: undefined,
  },
  channelNodeId: {
    type: Number,
    default: undefined,
  },
});

const { route } = useRouter();

// Variables
const tradingPartnerId = route.params.tradingPartnerId as string;

// Stores
const currentCoreTradingPartnerStore = useCurrentCoreTradingPartnerStore();
const tradingPartnersStore = useCoreTradingPartnersStore();

// Refs
const dynamicAssignedOutputs = ref<DynamicOutputLinkResponse[]>([]);
const coreAssignedOutputs = ref<CoreOutputLinkResponse[]>([]);

// Composables
const { t } = useI18n();
const { showDialog, cancel, confirmSuccess } = useDialog();
const { currentSyndications, currentDynamicMappings, currentMappings } = storeToRefs(currentCoreTradingPartnerStore);
const { coreFormats, dynamicFormats } = storeToRefs(tradingPartnersStore);

const reviewCollection = computed(() => {
  if (props.collectionType === 'channel') {
    return {
      id: `channel-${props.channelId}-${props.channelNodeId}`,
      text: props.collectionName,
      type: CollectionTypes.CHANNEL,
      metadata: {
        channelId: props.channelId || 0,
        channelNodeId: props.channelNodeId || 0,
        channelName: props.collectionName,
        channelNodeName: '',
      } as ChannelLinkResponse,
    } as Collection<ChannelLinkResponse>;
  } else if (props.collectionType === 'workarea') {
    return {
      id: props.workareaId,
      text: props.collectionName,
      type: CollectionTypes.WORKAREA,
      metadata: { id: props.workareaId, text: props.collectionName } as Workarea,
    } as Collection<Workarea>;
  } else {
    return {
      id: 'custom-selection',
      text: props.collectionName,
      type: CollectionTypes.WORKAREA,
      metadata: { id: 'custom-selection', text: props.collectionName } as Workarea,
    } as Collection<Workarea>;
  }
});

const { isLoading, confirmIsDisabled, selectedMapping, outputs, selectedOutput, init, onConfirm } =
  useRunSyndicationDialog(
    currentSyndications,
    currentMappings,
    currentDynamicMappings,
    coreFormats,
    dynamicFormats,
    reviewCollection.value,
    dynamicAssignedOutputs,
    coreAssignedOutputs
  );

// Functions
const getOptionLabel = (output: Output) =>
  output.OutputFormat
    ? `${output.ExtensionDisplayName} (${output.OutputFormat?.toLocaleLowerCase()})`
    : `${output.ExtensionDisplayName}`;

const confirm = async () => {
  const entityIds = props.collectionType === 'entities' ? props.selectedEntityIds : undefined;
  const jobResult = await onConfirm(entityIds);
  if (jobResult?.status === ResponseStatus.Success) {
    confirmSuccess(t('core.trading_partners.history.syndication_started'));
  } else if (jobResult?.status === ResponseStatus.Unavailable) {
    notify.error(t('core.trading_partners.history.syndication_unavailable'));
  } else {
    notify.error(t('core.trading_partners.history.syndication_error'));
  }
};

onBeforeMount(async () => {
  isLoading.value = true;
  try {
    await currentCoreTradingPartnerStore.init(tradingPartnerId);
    dynamicAssignedOutputs.value = await fetchDynamicAssignedOutputs(tradingPartnerId);
    coreAssignedOutputs.value = await fetchCoreAssignedOutputs(tradingPartnerId);
    if (props.isDynamic) {
      const dm = currentDynamicMappings.value.find((x) => x.id === props.mappingId);
      if (dm) {
        selectedMapping.value = {
          id: 'selected-dynamic-mapping',
          text: dm.name,
          metadata: dm as DynamicMappingResponse,
          type: MappingTypes.DYNAMIC_CORE_MAPPING,
        };
      }
    } else {
      const m = currentMappings.value.find((x) => x.MappingId === props.mappingId);
      if (m) {
        selectedMapping.value = {
          id: 'selected-core-mapping',
          text: m.MappingName,
          metadata: m as Mapping,
          type: MappingTypes.CORE_MAPPING,
        };
      }
    }
  } finally {
    isLoading.value = false;
  }

  init();
});

watchEffect(() => {
  if (outputs.value?.length && !selectedOutput.value) {
    selectedOutput.value = outputs.value[0] as Output;
  }
});
</script>
