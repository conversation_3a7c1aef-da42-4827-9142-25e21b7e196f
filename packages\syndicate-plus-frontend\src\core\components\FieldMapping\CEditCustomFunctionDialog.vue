<template>
  <c-dialog
    class="c-dialog"
    :title="currentFunctionModel.Name"
    :model-value="showDialog"
    :disable-confirm="confirmButtonIsDisabled"
    :confirm-button-text="$t('syndicate_plus.common.save')"
    @confirm="onConfirm"
    @cancel="onCancel"
  >
    <q-form ref="form" greedy autofocus>
      <q-input
        v-model="currentFunctionModel.Name"
        v-bind="$inri.input"
        :label="$t('core.custom_functions.function_name')"
        :rules="[$validate.required()]"
        autofocus
      />
      <prism-editor
        v-model="currentFunctionModel.Script"
        class="custom-function-script-editor mb-4"
        :highlight="highlighter"
        line-numbers
      />
    </q-form>
    <c-btn
      class="px-5 mt-2 pb-10"
      :label="$t('core.custom_functions.validate_function')"
      color="primary"
      @click="onValidate"
    />
  </c-dialog>
</template>

<script setup lang="ts">
import { PropType, ref, computed } from 'vue';
import { FunctionModel } from '@core/interfaces';
import { PrismEditor } from 'vue-prism-editor';
import { useDialog, notify } from '@inriver/inri';
import { useI18n } from 'vue-i18n';
import 'vue-prism-editor/dist/prismeditor.min.css';
import prism from 'prismjs';
import 'prismjs/themes/prism-tomorrow.css';

const props = defineProps({
  functionModel: {
    type: Object as PropType<FunctionModel>,
    required: true,
  },
});

const emit = defineEmits(['handle-confirm', 'handle-cancel', 'handle-validate']);

// Composables
const { showDialog, cancel } = useDialog();
const { t } = useI18n();

// Refs
const form = ref();
const currentFunctionModel = ref<FunctionModel>(props.functionModel);

// Computed
const confirmButtonIsDisabled = computed(() => !currentFunctionModel.value.Name || !currentFunctionModel.value.Script);

// Functions
const highlighter = (code) => {
  return prism.highlight(code, prism.languages.js);
};

const onConfirm = () => {
  form.value.validate().then((success) => {
    if (success) {
      emit('handle-confirm');
    } else {
      notify.error(t('core.custom_functions.validation_error'));
    }
  });
};

const onValidate = () => {
  emit('handle-validate');
};

const onCancel = () => {
  cancel();
  emit('handle-cancel');
};
</script>

<style lang="scss" scoped>
.custom-function-script-editor {
  background: #2d2d2d;
  color: #ccc;
  font-family: Fira code, Fira Mono, Consolas, Menlo, Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 5px;
  max-height: 250px;
  overflow: auto;
}
:deep(.prism-editor__textarea:focus) {
  outline: none;
}
</style>
