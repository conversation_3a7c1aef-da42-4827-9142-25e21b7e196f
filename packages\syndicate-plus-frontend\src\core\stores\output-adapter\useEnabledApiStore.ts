import { defineStore } from 'pinia';
import { fetchEnabledTradingPartnersFromApi } from '@core/services/TradingPartners';
import { getEnvironmentGlobalId } from '@helpers/EnvironmentHelper';
import { ref } from 'vue';

export const useEnabledApiStore = defineStore('coreEnabledApiStore', () => {
  const enabledApis = ref<string[]>([]);

  // Functions
  const fetchEnabledApis = async (): Promise => {
    if (enabledApis?.value?.length > 0) {
      return;
    }
    const bestBuyEnvironments = ['prod-marshallgroup-prod'];
    const enabledTradingPartners = await fetchEnabledTradingPartnersFromApi();
    const environmentGid = getEnvironmentGlobalId();
    const apiIds = enabledTradingPartners.map((api) => api.tradingPartnerId);
    if (bestBuyEnvironments.includes(environmentGid)) {
      if (!enabledTradingPartners.includes('best-buy')) {
        apiIds.push('best-buy');
      }
    }
    enabledApis.value = apiIds;
  };

  return {
    fetchEnabledApis,
    enabledApis,
  };
});
