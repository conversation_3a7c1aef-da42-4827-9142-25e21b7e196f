<template>
  <section class="panel-container">
    <teleport v-if="isTeleportEnabled" to="#main-section">
      <c-edit-custom-function-dialog
        v-if="showEditCustomFunctionDialog"
        v-model:show="showEditCustomFunctionDialog"
        :function-model="functionModel"
        @handle-validate="handleValidate"
        @handle-confirm="handleConfirm"
      />
    </teleport>
    <div v-if="isLoading" class="spinner w-70px m-auto">
      <c-spinner />
    </div>
    <div v-else>
      <c-separated-expansion-item
        key="default"
        :label="$t('syndicate_plus.mapping.function_mapping_tab.default_functions')"
        is-expanded
      >
        <draggable
          v-if="isDsaMappingPanelActive"
          v-model="defaultFunctionsDsa"
          item-key="id"
          ghost-class="builder-ghost"
          :group="{
            name: 'dsaFieldMapping',
            pull: 'clone',
            put: false,
          }"
          :sort="false"
          :move="dragStore.moveField"
          @start="dragStore.onDragStart"
        >
          <template #item="{ element }">
            <div class="function-card cursor-pointer space-x">
              <span class="function-name-text">
                {{ $t(defaultFunctionsSettings[element.Name]?.displayName ?? '') }}
              </span>
              <q-icon :name="defaultFunctionsSettings[element.Name]?.icon" size="xs" class="action-icon">
                <q-tooltip>{{ $t(defaultFunctionsSettings[element.Name]?.displayName ?? '') }}</q-tooltip>
              </q-icon>
            </div>
          </template>
        </draggable>
        <draggable
          v-else
          v-model="defaultFunctions"
          item-key="id"
          ghost-class="builder-ghost"
          :group="{
            name: 'fieldMapping',
            pull: 'clone',
            put: false,
          }"
          :sort="false"
          :move="dragStore.moveField"
          @start="dragStore.onDragStart"
        >
          <template #item="{ element }">
            <div class="function-card cursor-pointer space-x">
              <span class="function-name-text">
                {{ $t(defaultFunctionsSettings[element.Name]?.displayName ?? '') }}
              </span>
              <q-icon :name="defaultFunctionsSettings[element.Name]?.icon" size="xs" class="action-icon">
                <q-tooltip>{{ $t(defaultFunctionsSettings[element.Name]?.displayName ?? '') }}</q-tooltip>
              </q-icon>
            </div>
          </template>
        </draggable>
      </c-separated-expansion-item>
      <c-separated-expansion-item
        key="custom"
        :label="$t('syndicate_plus.mapping.function_mapping_tab.custom_functions')"
        is-expanded
      >
        <draggable
          v-model="customFunctions"
          item-key="id"
          ghost-class="builder-ghost"
          :group="{
            name: isDsaMappingPanelActive ? 'dsaFieldMapping' : 'fieldMapping',
            pull: 'clone',
            put: false,
          }"
          :sort="false"
          :move="dragStore.moveField"
          @start="dragStore.onDragStart"
        >
          <template #item="{ element }">
            <div class="custom function-card cursor-pointer">
              <div class="function-name">
                <q-tooltip>{{ element.Name }}</q-tooltip>
                <span class="function-name-text">{{ element.Name }}</span>
              </div>
              <div class="custom-function-actions">
                <q-icon
                  name="mdi-pencil-outline"
                  class="edit-custom-function"
                  size="xs"
                  @click="editCustomFunction(element)"
                />
                <q-icon
                  name="mdi-delete-outline"
                  class="delete-custom-function"
                  size="xs"
                  @click="deleteCustomFunction((element as FunctionModel).Id)"
                />
              </div>
            </div>
          </template>
        </draggable>
        <div class="new function-card cursor-pointer space-x" @click="addCustomFunction">
          {{ $t('core.custom_functions.new_function') }}
          <q-icon name="mdi-plus" class="action-item" size="xs"></q-icon>
        </div>
      </c-separated-expansion-item>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { storeToRefs } from 'pinia';
import draggable from 'vuedraggable';
import { notify } from '@inriver/inri';
import { useI18n } from 'vue-i18n';
import { defaultFunctionsSettings } from '@core/Utils';
import { useEditFieldMappingDragStore, useFunctionsStore } from '@core/stores';
import { CSeparatedExpansionItem } from '@components/Shared';
import { CEditCustomFunctionDialog } from '@core/components/FieldMapping';
import { FunctionModel } from '@core/interfaces';

defineProps({
  isDsaMappingPanelActive: {
    type: Boolean,
    default: false,
  },
});

const dragStore = useEditFieldMappingDragStore();
const functionsStore = useFunctionsStore();

// Composables
const { t } = useI18n();

// Refs
const isTeleportEnabled = ref(false);
const { isLoading, defaultFunctions, customFunctions, defaultFunctionsDsa } = storeToRefs(functionsStore);
const functionModel = ref<FunctionModel>({} as FunctionModel);
const showEditCustomFunctionDialog = ref(false);

// Functions
const addCustomFunction = () => {
  removeTextSelection();
  functionModel.value = { ...functionsStore.defaultCustomFunction };
  showEditCustomFunctionDialog.value = true;
};

const editCustomFunction = (element: FunctionModel) => {
  removeTextSelection();
  functionModel.value = { ...element };
  showEditCustomFunctionDialog.value = true;
};

const handleValidate = async (): Promise<void> => {
  const errorMessage = await functionsStore.validateAPICustomFunction(functionModel.value.Script);
  errorMessage ? notify.error(errorMessage) : notify.success(t('core.custom_functions.parse_script_success'));
};

const handleConfirm = async (): Promise<void> => {
  const errorSaveMessage = await functionsStore.saveAPICustomFunction(functionModel.value);
  if (errorSaveMessage) {
    notify.error(errorSaveMessage);
    return;
  } else {
    notify.success(t('core.custom_functions.save_custom_function_success'));
    await functionsStore.fetchAllFunctions();
    showEditCustomFunctionDialog.value = false;
  }
};

const deleteCustomFunction = async (id: number): Promise<void> => {
  const responseStatus = await functionsStore.deleteAPICustomFunction(id);
  if (responseStatus === 400) {
    notify.error(t('core.custom_functions.function_in_use_error_text'));
  } else if (responseStatus === 204 || responseStatus === 200) {
    notify.success(t('core.custom_functions.function_removed'));
    await functionsStore.fetchAllFunctions();
    return;
  } else {
    notify.error(t('core.custom_functions.function_removed_error_text'));
  }
};

const removeTextSelection = () => document?.getSelection()?.removeAllRanges();

// Lifecycle methods
onMounted(() => {
  isTeleportEnabled.value = true;
});
</script>

<style lang="scss" scoped>
.function-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid var(--color-grey-lighter);
  border-radius: 6px;
  margin-top: 10px;
  position: relative;
  min-height: 36px;
  transition: all 0.2s ease;

  .function-name {
    flex: 1;
    min-width: 0; // Allow shrinking
    margin-right: 8px;

    .function-name-text {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.4;
      word-break: break-word;
      overflow-wrap: break-word;
      max-width: 100%;
      white-space: nowrap;
    }
  }

  &.custom {
    .custom-function-actions {
      display: flex;
      gap: 2px;
      margin-left: auto;
      flex-shrink: 0;

      .edit-custom-function,
      .delete-custom-function {
        opacity: 0;
        transition: all 0.2s ease;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 20px;
        min-height: 20px;

        &:hover {
          background-color: var(--color-grey-lighter);
          transform: scale(1.1);
        }
      }
    }

    &:hover {
      background-color: var(--color-grey-10);
      border-color: var(--color-grey-light);
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      .custom-function-actions {
        .edit-custom-function,
        .delete-custom-function {
          opacity: 1;
        }
      }
    }
  }

  &.new:hover {
    background-color: var(--color-grey-10);
    border-color: var(--color-grey-light);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
}

.field-ghost {
  display: none;
}

.dragging {
  .field-ghost {
    display: block;
    margin-bottom: 10px;
  }
}
</style>
