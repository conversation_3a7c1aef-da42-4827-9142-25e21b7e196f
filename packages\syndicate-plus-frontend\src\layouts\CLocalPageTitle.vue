<template>
  <div class="flex-1 flex h-15 items-center overflow-hidden px-6" :class="noBorder || 'border-b'">
    <slot name="back-button" />
    <h1 data-id="page-title" class="truncate">
      <slot>
        {{ title || routeTitle }}
      </slot>
    </h1>
    <div class="ml-2">
      <slot name="additional-info" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { getRouteTitle } from '@inriver/inri/src/utils/routeUtils';
import { computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

const props = defineProps({
  title: {
    type: String,
    default: undefined,
  },
  noBorder: {
    type: Boolean,
    default: false,
  },
  noRouteTitle: {
    type: Boolean,
    default: false,
  },
});

const routeTitle = computed(() => (props.noRouteTitle ? '' : getRouteTitle(route)));
</script>
