import { describe, it, expect } from 'vitest';
import { getEnumKeyByValue } from './EnumHelper';
import { SaveStatus } from '@enums/SaveStatus';
import { AmazonCallbackUrlParams } from '@enums/Amazon';

describe('EnumHelper', () => {
  describe('getEnumKeyByValue', () => {
    describe('with string enum values', () => {
      it('when valid string enum value is provided, should return correct key', () => {
        // Arrange
        const enumValue = 'saving';

        // Act
        const result = getEnumKeyByValue(SaveStatus, enumValue);

        // Assert
        expect(result).toBe('SAVING');
      });

      it('when SaveStatus.UNDEFINED string enum value is provided, should return UNDEFINED', () => {
        // Arrange
        const enumValue = '';

        // Act
        const result = getEnumKeyByValue(SaveStatus, enumValue);

        // Assert
        expect(result).toBe('UNDEFINED');
      });

      it('when AmazonCallbackUrlParams enum values are provided, should return correct keys', () => {
        // Arrange & Act & Assert
        expect(getEnumKeyByValue(AmazonCallbackUrlParams, 'return-from-amazon')).toBe('RETURN_FROM_AMAZON');
        expect(getEnumKeyByValue(AmazonCallbackUrlParams, 'status')).toBe('STATUS');
      });

      it('when non-existent string value is provided, should return undefined', () => {
        // Arrange
        const nonExistentValue = 'nonexistent';

        // Act
        const result = getEnumKeyByValue(SaveStatus, nonExistentValue);

        // Assert
        expect(result).toBeUndefined();
      });

      it('when null value is provided, should return undefined', () => {
        // Arrange
        const nullValue = null as any;

        // Act
        const result = getEnumKeyByValue(SaveStatus, nullValue);

        // Assert
        expect(result).toBeUndefined();
      });

      it('when undefined value is provided, should return undefined', () => {
        // Arrange
        const undefinedValue = undefined as any;

        // Act
        const result = getEnumKeyByValue(SaveStatus, undefinedValue);

        // Assert
        expect(result).toBeUndefined();
      });
    });

    describe('with numeric enum values', () => {
      // Create a test enum with numeric values
      enum NumericTestEnum {
        FIRST = 0,
        SECOND = 1,
        THIRD = 5,
        FOURTH = 10,
      }

      it('when valid numeric enum value is provided, should return correct key', () => {
        // Arrange
        const numericValue = 1;

        // Act
        const result = getEnumKeyByValue(NumericTestEnum, numericValue);

        // Assert
        expect(result).toBe('SECOND');
      });

      it('when zero numeric value is provided, should return correct key', () => {
        // Arrange
        const zeroValue = 0;

        // Act
        const result = getEnumKeyByValue(NumericTestEnum, zeroValue);

        // Assert
        expect(result).toBe('FIRST');
      });

      it('when non-sequential numeric value is provided, should return correct key', () => {
        // Arrange
        const nonSequentialValue = 5;

        // Act
        const result = getEnumKeyByValue(NumericTestEnum, nonSequentialValue);

        // Assert
        expect(result).toBe('THIRD');
      });

      it('when larger numeric value is provided, should return correct key', () => {
        // Arrange
        const largerValue = 10;

        // Act
        const result = getEnumKeyByValue(NumericTestEnum, largerValue);

        // Assert
        expect(result).toBe('FOURTH');
      });

      it('when non-existent numeric value is provided, should return undefined', () => {
        // Arrange
        const nonExistentValue = 99;

        // Act
        const result = getEnumKeyByValue(NumericTestEnum, nonExistentValue);

        // Assert
        expect(result).toBeUndefined();
      });

      it('when negative numeric value not in enum is provided, should return undefined', () => {
        // Arrange
        const negativeValue = -1;

        // Act
        const result = getEnumKeyByValue(NumericTestEnum, negativeValue);

        // Assert
        expect(result).toBeUndefined();
      });
    });

    describe('with mixed enum values', () => {
      // Create a test enum with mixed string and numeric values
      enum MixedTestEnum {
        ZERO = 0,
        ONE = 1,
        TEXT = 'text',
        EMPTY = '',
      }

      it('when mixed enum numeric values are provided, should handle correctly', () => {
        // Arrange & Act & Assert
        expect(getEnumKeyByValue(MixedTestEnum, 0)).toBe('ZERO');
        expect(getEnumKeyByValue(MixedTestEnum, 1)).toBe('ONE');
      });

      it('when mixed enum string values are provided, should handle correctly', () => {
        // Arrange & Act & Assert
        expect(getEnumKeyByValue(MixedTestEnum, 'text')).toBe('TEXT');
        expect(getEnumKeyByValue(MixedTestEnum, '')).toBe('EMPTY');
      });

      it('when non-existent values are provided in mixed enum, should return undefined', () => {
        // Arrange & Act & Assert
        expect(getEnumKeyByValue(MixedTestEnum, 'nonexistent')).toBeUndefined();
        expect(getEnumKeyByValue(MixedTestEnum, 99)).toBeUndefined();
      });
    });

    describe('edge cases and type safety', () => {
      it('when empty enum object is provided, should handle correctly', () => {
        // Arrange
        const emptyEnum = {};
        const testValue = 'any';

        // Act
        const result = getEnumKeyByValue(emptyEnum, testValue);

        // Assert
        expect(result).toBeUndefined();
      });

      it('when string values are case-sensitive, should be handled correctly', () => {
        // Arrange
        const uppercaseValue = 'SAVING'; // uppercase instead of lowercase

        // Act
        const result = getEnumKeyByValue(SaveStatus, uppercaseValue);

        // Assert
        expect(result).toBeUndefined();
      });

      it('when string representation of numbers is provided for numeric enums, should not match', () => {
        // Arrange
        enum NumericEnum {
          FIRST = 1,
          SECOND = 2,
        }
        const stringNumber = '1' as any; // Should not match string '1' with numeric 1

        // Act
        const result = getEnumKeyByValue(NumericEnum, stringNumber);

        // Assert
        expect(result).toBeUndefined();
      });

      it('when multiple keys have same value, should return first matching key', () => {
        // Arrange
        enum DuplicateValueEnum {
          FIRST = 'duplicate',
          SECOND = 'duplicate',
          THIRD = 'unique',
        }
        const duplicateValue = 'duplicate';

        // Act
        const result = getEnumKeyByValue(DuplicateValueEnum, duplicateValue);

        // Assert
        expect(result).toBe('FIRST'); // Should return the first key found with this value
      });
    });
  });
});
