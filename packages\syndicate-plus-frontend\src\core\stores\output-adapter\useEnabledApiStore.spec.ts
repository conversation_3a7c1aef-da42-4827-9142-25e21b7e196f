import { setActivePinia, createPinia } from 'pinia';
import { describe, it, vitest, expect, vi, beforeEach } from 'vitest';
import { useEnabledApiStore } from './useEnabledApiStore';
import * as EnvironmentHelper from '@helpers/EnvironmentHelper';
import { fetchEnabledTradingPartnersFromApi } from '@core/services/TradingPartners';

vitest.mock('@core/services/TradingPartners');

const mockFetchEnabledTradingPartners = fetchEnabledTradingPartnersFromApi as jest.MockedFunction<
  typeof fetchEnabledTradingPartnersFromApi
>;

const bestBuyId = 'best-buy';
describe('useEnabledApiStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    mockFetchEnabledTradingPartners.mockResolvedValue([]);
  });

  it(`returns ["${bestBuyId}"] for prod-marshallgroup-prod`, async () => {
    vi.spyOn(EnvironmentHelper, 'getEnvironmentGlobalId').mockReturnValue('prod-marshallgroup-prod');
    const store = useEnabledApiStore();

    await store.fetchEnabledApis();
    expect(store.enabledApis).toEqual([bestBuyId]);
  });

  it('returns [] for other environments', async () => {
    vi.spyOn(EnvironmentHelper, 'getEnvironmentGlobalId').mockReturnValue('some-other-env');
    const store = useEnabledApiStore();

    await store.fetchEnabledApis();
    expect(store.enabledApis).toEqual([]);
  });
});
