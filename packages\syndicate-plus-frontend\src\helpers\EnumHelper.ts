/**
 * Gets the enum key by its value
 * @param enumObject The enum object to search in
 * @param value The value to find the key for
 *
 * @returns The key corresponding to the given value, or undefined if not found
 */
export function getEnumKeyByValue<T extends { [index: string]: string | number }>(
  enumObject: T,
  value: string | number
): string | undefined {
  return Object.keys(enumObject).find((key) => enumObject[key] === value);
}
