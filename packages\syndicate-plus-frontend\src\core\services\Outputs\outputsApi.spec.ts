import { describe, expect, it, vi, beforeEach } from 'vitest';

// Mock the utility functions
const mockPortalFetch = vi.fn();
const mockProxyPortalFetch = vi.fn();

vi.mock('@utils', () => ({
  portalFetch: mockPortalFetch,
  proxyPortalFetch: mockProxyPortalFetch,
}));

describe('outputsApi', () => {
  const mockOutputId = 'test-output-id';

  describe('fetchOutputById', () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    it('should return transformed OutputResponse when API returns valid data', async () => {
      // We'll test the function regardless of environment
      const { fetchOutputById } = await import('./outputsApi');

      const mockApiResponse = {
        OutputName: 'test-output',
        OutputType: 'excel',
        Settings: {
          EnableCompression: 'true',
          UseZip: 'false',
          UseUTF8: 'true',
          ExportExcelStartRow: '10',
          ExportExcelStartColumn: '5',
          OutputFormat: 'excel',
          DeliveryMethods: 'FTP',
          Delimiter: ',',
          FTPS_Port: '990',
        },
      };

      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue(mockApiResponse),
      } as unknown as Response;

      // Mock both functions to ensure one gets called
      mockPortalFetch.mockResolvedValue(mockResponse);
      mockProxyPortalFetch.mockResolvedValue(mockResponse);

      const result = await fetchOutputById(mockOutputId);

      // Check that one of the fetch functions was called
      const totalCalls = mockPortalFetch.mock.calls.length + mockProxyPortalFetch.mock.calls.length;
      expect(totalCalls).toBeGreaterThan(0);

      // Verify the result has been transformed correctly
      expect(result).toEqual({
        ExtensionId: 'test-output',
        DisplayName: 'test-output',
        Settings: {
          EnableCompression: true,
          UseZip: false,
          UseUTF8: true,
          ExportExcelStartRow: 10,
          ExportExcelStartColumn: 5,
          OutputFormat: 'excel',
          DeliveryMethods: 'FTP',
          Delimiter: ',',
          FTPS_Port: '990',
        },
      });
    });

    it('should handle null response', async () => {
      const { fetchOutputById } = await import('./outputsApi');

      mockPortalFetch.mockResolvedValue(null);
      mockProxyPortalFetch.mockResolvedValue(null);

      const result = await fetchOutputById(mockOutputId);
      expect(result).toBeNull();
    });

    it('should handle empty settings', async () => {
      const { fetchOutputById } = await import('./outputsApi');

      const mockApiResponse = {
        OutputName: 'test-output',
        OutputType: 'excel',
        Settings: null,
      };

      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue(mockApiResponse),
      } as unknown as Response;

      mockPortalFetch.mockResolvedValue(mockResponse);
      mockProxyPortalFetch.mockResolvedValue(mockResponse);

      const result = await fetchOutputById(mockOutputId);

      expect(result).toEqual({
        ExtensionId: 'test-output',
        DisplayName: 'test-output',
        Settings: null,
      });
    });

    it('should correctly transform string booleans to actual booleans', async () => {
      const { fetchOutputById } = await import('./outputsApi');

      const mockApiResponse = {
        OutputName: 'test',
        OutputType: 'excel',
        Settings: {
          EnableCompression: 'true',
          UseZip: 'false',
          UseUTF8: 'TRUE',
        },
      };

      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue(mockApiResponse),
      } as unknown as Response;

      mockPortalFetch.mockResolvedValue(mockResponse);
      mockProxyPortalFetch.mockResolvedValue(mockResponse);

      const result = await fetchOutputById(mockOutputId);

      expect((result?.Settings as any)?.EnableCompression).toBe(true);
      expect((result?.Settings as any)?.UseZip).toBe(false);
      expect((result?.Settings as any)?.UseUTF8).toBe(true);
    });

    it('should correctly transform string numbers to actual numbers', async () => {
      const { fetchOutputById } = await import('./outputsApi');

      const mockApiResponse = {
        OutputName: 'test',
        OutputType: 'excel',
        Settings: {
          ExportExcelStartRow: '15',
          ExportExcelStartColumn: '3',
        },
      };

      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue(mockApiResponse),
      } as unknown as Response;

      mockPortalFetch.mockResolvedValue(mockResponse);
      mockProxyPortalFetch.mockResolvedValue(mockResponse);

      const result = await fetchOutputById(mockOutputId);

      expect((result?.Settings as any)?.ExportExcelStartRow).toBe(15);
      expect((result?.Settings as any)?.ExportExcelStartColumn).toBe(3);
    });

    it('should handle mixed data types correctly', async () => {
      const { fetchOutputById } = await import('./outputsApi');

      const mockApiResponse = {
        OutputName: 'mixed-test',
        OutputType: 'excel',
        Settings: {
          EnableCompression: true, // already boolean
          UseZip: 'false', // string boolean
          ExportExcelStartRow: 10, // already number
          ExportExcelStartColumn: '5', // string number
          OutputFormat: 'csv', // string
          Delimiter: ',', // string
        },
      };

      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue(mockApiResponse),
      } as unknown as Response;

      mockPortalFetch.mockResolvedValue(mockResponse);
      mockProxyPortalFetch.mockResolvedValue(mockResponse);

      const result = await fetchOutputById(mockOutputId);

      expect((result?.Settings as any)?.EnableCompression).toBe(true);
      expect((result?.Settings as any)?.UseZip).toBe(false);
      expect((result?.Settings as any)?.ExportExcelStartRow).toBe(10);
      expect((result?.Settings as any)?.ExportExcelStartColumn).toBe(5);
      expect((result?.Settings as any)?.OutputFormat).toBe('csv');
      expect((result?.Settings as any)?.Delimiter).toBe(',');
    });
  });
});
