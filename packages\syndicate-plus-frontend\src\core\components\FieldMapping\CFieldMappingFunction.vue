<template>
  <div v-if="hasFunction" class="c-field-mapping-function">
    <q-card-section :horizontal="true">
      <div class="flex-container">
        <q-card dense>
          <div class="color-custom-card function-name">
            {{ functionName }}
          </div>
        </q-card>
      </div>
    </q-card-section>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, watchEffect } from 'vue';
import { useFieldMappingFunction } from '@core/composables';
import { useFunctionsStore } from '@core/stores';
import { storeToRefs } from 'pinia';

const props = defineProps({
  converterId: {
    type: Number,
    default: 0,
  },
});

const functionsStore = useFunctionsStore();

// Composables
const { functionName, hasFunction, matchFunctionName } = useFieldMappingFunction();

// Refs
const { customFunctions, defaultFunctions } = storeToRefs(functionsStore);

// Lifecycle methods
onBeforeMount(() => matchFunctionName(props.converterId, [...customFunctions.value, ...defaultFunctions.value]));

watchEffect(() => {
  matchFunctionName(props.converterId, [...customFunctions.value, ...defaultFunctions.value]);
});
</script>

<style lang="scss" scoped>
.c-field-mapping-function {
  font-size: 10px;
  padding: 0px;

  .q-card {
    border-radius: 30px;
    box-shadow: none;
  }
}

.c-field-mapping-function .q-card > div:last-child,
.q-card > img:last-child {
  border-radius: 30px;
  border: 1.5px solid var(--color-grey-dark);
  padding: 4px;
  box-shadow: none;
}

.color-card,
.color-custom-card {
  background-color: var(--color-primary);
  height: 20px;
  line-height: 11px;

  &.function-name {
    background-color: var(--color-grey-lighter);
  }
}
</style>
