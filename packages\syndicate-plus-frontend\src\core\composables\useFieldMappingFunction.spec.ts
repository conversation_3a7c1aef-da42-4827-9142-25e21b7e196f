import { describe, it, expect } from 'vitest';
import { useFieldMappingFunction } from '@core/composables';
import { FunctionModel } from '@core/interfaces';
import { DefaultFunctionsName } from '@core/enums';

describe('useFieldMappingFunction', () => {
  describe('parseFunctionName', () => {
    it('should parse function name from converter args correctly', async () => {
      const testCases = [
        {
          converterArgs:
            '{"transformations":[{"function":{"name":"BeforeAfter","args":[null,"","en",true],"values":["test","test"]}}]}',
          expectedValue: 'BeforeAfter',
        },
        { converterArgs: '', expectedValue: '' },
        { converterArgs: undefined, expectedValue: '' },
        {
          converterArgs: '{"transformations":[{"function":{"name":"name","args":["",""],"values":[]}}]}',
          expectedValue: 'name',
        },
      ];
      testCases.forEach(({ converterArgs, expectedValue }) => {
        // Arrange
        const { functionName, parseFunctionName } = useFieldMappingFunction();

        // Act
        parseFunctionName(converterArgs);

        // Assert
        expect(functionName.value).toBe(expectedValue);
      });
    });
  });

  describe('matchFunctionName', () => {
    it('should set function name when matching function is found', () => {
      // Arrange
      const mockFunctions: FunctionModel[] = [
        {
          Id: 1,
          Name: DefaultFunctionsName.ToUpper,
          ImageClass: 'fa fa-arrow-up',
          Script: 'function toUpper() {}',
          IsCustom: false,
        },
        {
          Id: 2,
          Name: DefaultFunctionsName.Concatenate,
          ImageClass: 'fa fa-plus',
          Script: 'function concatenate() {}',
          IsCustom: false,
        },
        {
          Id: 3,
          Name: 'CustomFunction',
          ImageClass: 'fa fa-cog',
          Script: 'function custom() {}',
          IsCustom: true,
        },
      ];
      const { functionName, matchFunctionName } = useFieldMappingFunction();

      // Act
      matchFunctionName(2, mockFunctions);

      // Assert
      expect(functionName.value).toBe(DefaultFunctionsName.Concatenate);
    });

    it('should set empty string when no matching function is found', () => {
      // Arrange
      const mockFunctions: FunctionModel[] = [
        {
          Id: 1,
          Name: DefaultFunctionsName.ToUpper,
          ImageClass: 'fa fa-arrow-up',
          Script: 'function toUpper() {}',
          IsCustom: false,
        },
      ];
      const { functionName, matchFunctionName } = useFieldMappingFunction();

      // Act
      matchFunctionName(999, mockFunctions);

      // Assert
      expect(functionName.value).toBe('');
    });

    it('should set empty string when functions array is empty', () => {
      // Arrange
      const mockFunctions: FunctionModel[] = [];
      const { functionName, matchFunctionName } = useFieldMappingFunction();

      // Act
      matchFunctionName(1, mockFunctions);

      // Assert
      expect(functionName.value).toBe('');
    });

    it('should handle custom functions correctly', () => {
      // Arrange
      const mockFunctions: FunctionModel[] = [
        {
          Id: 100,
          Name: 'MyCustomFunction',
          ImageClass: 'fa fa-cog',
          Script: 'function myCustom() { return "custom"; }',
          IsCustom: true,
        },
      ];
      const { functionName, matchFunctionName } = useFieldMappingFunction();

      // Act
      matchFunctionName(100, mockFunctions);

      // Assert
      expect(functionName.value).toBe('MyCustomFunction');
    });

    it('should handle function with undefined Name property', () => {
      // Arrange
      const mockFunctions: FunctionModel[] = [
        {
          Id: 1,
          Name: undefined as any,
          ImageClass: 'fa fa-question',
          Script: 'function unknown() {}',
          IsCustom: false,
        },
      ];
      const { functionName, matchFunctionName } = useFieldMappingFunction();

      // Act
      matchFunctionName(1, mockFunctions);

      // Assert
      expect(functionName.value).toBe('');
    });
  });
});
