<template>
  <div v-if="showBanners" class="c-banner-container q-gutter-sm" data-testid="banner-container">
    <!-- Success Banner -->
    <q-banner
      v-if="bannerState.success.show"
      dense
      inline-actions
      rounded
      class="bg-green-1 text-green-9 q-mb-sm"
      data-testid="success-banner"
    >
      <template #avatar>
        <q-icon class="q-pa-sm" name="mdi-check-circle-outline" color="green" size="16px" />
      </template>

      {{ bannerState.success.message }}

      <template v-if="bannerState.success.showClose" #action>
        <q-btn
          flat
          dense
          round
          size="sm"
          icon="mdi-close"
          color="green"
          data-testid="success-banner-close"
          @click="banner.hide('success')"
        />
      </template>
    </q-banner>

    <!-- Info Banner -->
    <q-banner
      v-if="bannerState.info.show"
      dense
      inline-actions
      rounded
      class="bg-blue-1 text-blue-9 q-mb-sm"
      data-testid="info-banner"
    >
      <template #avatar>
        <q-icon class="q-pa-sm" name="mdi-information-outline" color="blue" size="16px" />
      </template>

      {{ bannerState.info.message }}

      <template v-if="bannerState.info.showClose" #action>
        <q-btn
          flat
          dense
          round
          size="sm"
          icon="mdi-close"
          color="blue"
          data-testid="info-banner-close"
          @click="banner.hide('info')"
        />
      </template>
    </q-banner>

    <!-- Warning Banner -->
    <q-banner
      v-if="bannerState.warning.show"
      dense
      inline-actions
      rounded
      class="bg-orange-1 text-orange-9 q-mb-sm"
      data-testid="warning-banner"
    >
      <template #avatar>
        <q-icon class="q-pa-sm" name="mdi-alert-outline" color="orange" size="16px" />
      </template>

      {{ bannerState.warning.message }}

      <template v-if="bannerState.warning.showClose" #action>
        <q-btn
          flat
          dense
          round
          size="sm"
          icon="mdi-close"
          color="orange"
          data-testid="warning-banner-close"
          @click="banner.hide('warning')"
        />
      </template>
    </q-banner>

    <!-- Error Banner -->
    <q-banner
      v-if="bannerState.error.show"
      dense
      inline-actions
      rounded
      class="bg-red-1 text-red-9 q-mb-sm"
      data-testid="error-banner"
    >
      <template #avatar>
        <q-icon class="q-pa-sm" name="mdi-alert-circle-outline" color="red" size="16px" />
      </template>

      {{ bannerState.error.message }}

      <template v-if="bannerState.error.showClose" #action>
        <q-btn
          flat
          dense
          round
          size="sm"
          icon="mdi-close"
          color="red"
          data-testid="error-banner-close"
          @click="banner.hide('error')"
        />
      </template>
    </q-banner>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { bannerState, banner } from './Utils/bannerNotify';

const showBanners = computed(() => {
  return Object.values(bannerState.value).some((bannerType) => bannerType.show);
});
</script>

<style scoped lang="scss">
.rounded-borders {
  border-radius: 5px;
}
</style>
